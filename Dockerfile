FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV TVM_HOME=/opt/tvm
ENV PYTHONPATH=$TVM_HOME/python:$PYTHONPATH

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    cmake \
    build-essential \
    libtinfo-dev \
    zlib1g-dev \
    libxml2-dev \
    llvm-dev \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /workspace

# 复制项目文件
COPY requirements.txt .
COPY build.py .
COPY web_stable_diffusion/ ./web_stable_diffusion/

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt

# 安装TVM Unity
RUN pip3 install mlc-ai-nightly -f https://mlc.ai/wheels

# 设置CUDA环境变量
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=$CUDA_HOME/bin:$PATH
ENV LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# 创建输出目录
RUN mkdir -p /workspace/dist

# 默认命令
CMD ["python3", "build.py", "--target", "cuda"]

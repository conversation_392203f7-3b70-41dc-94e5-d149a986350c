services:
  web-stable-diffusion:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web-stable-diffusion-build
    volumes:
      # 挂载项目目录
      - .:/workspace
      # 缓存目录，避免重复下载
      - pip-cache:/root/.cache/pip
      - huggingface-cache:/root/.cache/huggingface
      - tvm-cache:/root/.tvm
    environment:
      - HF_HOME=/root/.cache/huggingface
    working_dir: /workspace
    command: python3 build.py --target llvm --use-cache=0
    
  # 用于CPU构建的服务
  web-stable-diffusion-cpu:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web-stable-diffusion-cpu-build
    volumes:
      - .:/workspace
      - pip-cache:/root/.cache/pip
      - huggingface-cache:/root/.cache/huggingface
      - tvm-cache:/root/.tvm
    environment:
      - HF_HOME=/root/.cache/huggingface
    working_dir: /workspace
    command: python3 build.py --target llvm --use-cache=0

  # Web服务器用于测试编译结果
  web-stable-diffusion-serve:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web-stable-diffusion-serve
    volumes:
      - .:/workspace
      - pip-cache:/root/.cache/pip
      - huggingface-cache:/root/.cache/huggingface
      - tvm-cache:/root/.tvm
    environment:
      - HF_HOME=/root/.cache/huggingface
    working_dir: /workspace
    ports:
      - "8080:8080"
    command: python3 -m http.server 8080

volumes:
  pip-cache:
    driver: local
  huggingface-cache:
    driver: local
  tvm-cache:
    driver: local
